// Types for the Labubu app generator

export interface BaseLububu {
  id: string;
  name: string;
  imageData: string; // base64 encoded image data
  imageType: string; // mime type (e.g., 'image/png', 'image/jpeg')
  description?: string;
  createdAt: Date;
}

export interface Persona {
  id: string;
  name: string;
  faceImageData?: string; // base64 encoded image data (optional)
  faceImageType?: string; // mime type
  personality: string;
  aiPrompt: string;
  description?: string;
  createdAt: Date;
}

export interface GeneratedLububu {
  id: string;
  baseLububuId: string;
  personaId: string;
  generatedImageUrl: string;
  generatedText: string;
  createdAt: Date;
}

export interface GenerationRequest {
  baseLububu: BaseLububu;
  persona: Persona;
}

export interface GenerationResult {
  imageUrl: string;
  text: string;
}
