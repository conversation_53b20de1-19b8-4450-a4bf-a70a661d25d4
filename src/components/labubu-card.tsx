
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Sparkles } from "lucide-react";

interface LabubuCardProps {
  imageUrl: string;
  hint: string;
  isFlipping: boolean;
}

export default function LabubuCard({
  imageUrl,
  hint,
  isFlipping,
}: LabubuCardProps) {
  return (
    <div
      className={cn(
        "relative w-full h-full preserve-3d transition-transform duration-1000",
        isFlipping && "rotate-y-180"
      )}
    >
      {/* Card Back */}
      <div className="absolute w-full h-full backface-hidden rounded-3xl bg-gradient-to-br from-primary via-purple-300 to-accent shadow-2xl flex flex-col items-center justify-center p-8 overflow-hidden">
        <div className="absolute inset-0 bg-black/5"></div>
        <Sparkles className="w-24 h-24 text-white/50 animate-pulse" />
        <h2 className="mt-4 text-3xl font-bold text-white tracking-widest uppercase">
          Lucky Labubu
        </h2>
      </div>

      {/* Card Front */}
      <div className="absolute w-full h-full backface-hidden rotate-y-180 rounded-3xl bg-card shadow-2xl overflow-hidden">
        {imageUrl && (
          <Image
            src={imageUrl}
            alt={hint || "A mysterious Labubu"}
            fill
            sizes="(max-width: 768px) 100vw, 50vw"
            className="object-cover"
            data-ai-hint={hint}
            priority={isFlipping}
          />
        )}
      </div>
    </div>
  );
}
