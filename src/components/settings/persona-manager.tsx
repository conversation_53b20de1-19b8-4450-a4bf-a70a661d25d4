"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2, Plus, User } from "lucide-react";
import { Persona } from "@/types/labubu";
import { DataManager } from "@/lib/data-manager";
import { useToast } from "@/hooks/use-toast";
import Image from "next/image";
import FileUpload from "@/components/ui/file-upload";

export default function PersonaManager() {
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [isAdding, setIsAdding] = useState(false);
  const [newPersona, setNewPersona] = useState({
    name: "",
    faceImageData: "",
    faceImageType: "",
    personality: "",
    aiPrompt: "",
    description: "",
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    setPersonas(DataManager.getPersonas());
  }, []);

  const handleFileSelect = async (file: File) => {
    try {
      const { data, type } = await DataManager.fileToBase64(file);
      setSelectedFile(file);
      setNewPersona({
        ...newPersona,
        faceImageData: data,
        faceImageType: type,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process image file.",
        variant: "destructive",
      });
    }
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    setNewPersona({
      ...newPersona,
      faceImageData: "",
      faceImageType: "",
    });
  };

  const handleAdd = () => {
    if (!newPersona.name.trim() || !newPersona.personality.trim() || !newPersona.aiPrompt.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide name, personality, and AI prompt.",
        variant: "destructive",
      });
      return;
    }

    try {
      const added = DataManager.addPersona(newPersona);
      setPersonas(DataManager.getPersonas());
      setNewPersona({ name: "", faceImageData: "", faceImageType: "", personality: "", aiPrompt: "", description: "" });
      setSelectedFile(null);
      setIsAdding(false);
      toast({
        title: "Success",
        description: "Persona added successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add persona.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = (id: string) => {
    try {
      DataManager.deletePersona(id);
      setPersonas(DataManager.getPersonas());
      toast({
        title: "Success",
        description: "Persona deleted successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete persona.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Personas</h3>
        <Button
          onClick={() => setIsAdding(!isAdding)}
          size="sm"
          variant={isAdding ? "outline" : "default"}
        >
          <Plus className="w-4 h-4 mr-2" />
          {isAdding ? "Cancel" : "Add New"}
        </Button>
      </div>

      {isAdding && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Add New Persona</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="persona-name">Name</Label>
              <Input
                id="persona-name"
                value={newPersona.name}
                onChange={(e) => setNewPersona({ ...newPersona, name: e.target.value })}
                placeholder="e.g., Luna the Dreamer"
              />
            </div>
            <div>
              <FileUpload
                label="Face Image (Optional)"
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                preview={newPersona.faceImageData || undefined}
                maxSize={2}
              />
            </div>
            <div>
              <Label htmlFor="personality">Personality</Label>
              <Textarea
                id="personality"
                value={newPersona.personality}
                onChange={(e) => setNewPersona({ ...newPersona, personality: e.target.value })}
                placeholder="Describe the personality traits and characteristics..."
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="ai-prompt">AI Generation Prompt</Label>
              <Textarea
                id="ai-prompt"
                value={newPersona.aiPrompt}
                onChange={(e) => setNewPersona({ ...newPersona, aiPrompt: e.target.value })}
                placeholder="Detailed instructions for AI to generate this persona's Labubu style..."
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="persona-description">Description (Optional)</Label>
              <Input
                id="persona-description"
                value={newPersona.description}
                onChange={(e) => setNewPersona({ ...newPersona, description: e.target.value })}
                placeholder="Short description of this persona"
              />
            </div>
            <Button onClick={handleAdd} className="w-full">
              Add Persona
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {personas.map((persona) => (
          <Card key={persona.id} className="relative">
            <CardContent className="p-4">
              <div className="flex items-start gap-3 mb-3">
                <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
                  {persona.faceImageData ? (
                    <Image
                      src={persona.faceImageData}
                      alt={persona.name}
                      width={48}
                      height={48}
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <User className="w-6 h-6 text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-sm mb-1">{persona.name}</h4>
                  {persona.description && (
                    <p className="text-xs text-muted-foreground mb-2">
                      {persona.description}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2 mb-4">
                <div>
                  <p className="text-xs font-medium text-gray-600 mb-1">Personality:</p>
                  <p className="text-xs text-gray-700 line-clamp-2">{persona.personality}</p>
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-600 mb-1">AI Prompt:</p>
                  <p className="text-xs text-gray-700 line-clamp-3">{persona.aiPrompt}</p>
                </div>
              </div>
              
              <Button
                onClick={() => handleDelete(persona.id)}
                size="sm"
                variant="destructive"
                className="w-full"
              >
                <Trash2 className="w-3 h-3 mr-1" />
                Delete
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {personas.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <User className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No personas yet. Add your first one!</p>
        </div>
      )}
    </div>
  );
}
