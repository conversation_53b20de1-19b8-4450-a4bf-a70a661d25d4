"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2, Plus, Upload } from "lucide-react";
import { BaseLububu } from "@/types/labubu";
import { DataManager } from "@/lib/data-manager";
import { useToast } from "@/hooks/use-toast";
import Image from "next/image";
import FileUpload from "@/components/ui/file-upload";

export default function BaseLububuManager() {
  const [lububus, setLububus] = useState<BaseLububu[]>([]);
  const [isAdding, setIsAdding] = useState(false);
  const [newLububu, setNewLububu] = useState({
    name: "",
    imageData: "",
    imageType: "",
    description: "",
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    setLububus(DataManager.getBaseLububus());
  }, []);

  const handleFileSelect = async (file: File) => {
    try {
      const { data, type } = await DataManager.fileToBase64(file);
      setSelectedFile(file);
      setNewLububu({
        ...newLububu,
        imageData: data,
        imageType: type,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process image file.",
        variant: "destructive",
      });
    }
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    setNewLububu({
      ...newLububu,
      imageData: "",
      imageType: "",
    });
  };

  const handleAdd = () => {
    if (!newLububu.name.trim() || !newLububu.imageData.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both name and image.",
        variant: "destructive",
      });
      return;
    }

    try {
      const added = DataManager.addBaseLububu({
        name: newLububu.name,
        imageData: newLububu.imageData,
        imageType: newLububu.imageType,
        description: newLububu.description,
      });
      setLububus(DataManager.getBaseLububus());
      setNewLububu({ name: "", imageData: "", imageType: "", description: "" });
      setSelectedFile(null);
      setIsAdding(false);
      toast({
        title: "Success",
        description: "Base Labubu added successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add base Labubu.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = (id: string) => {
    try {
      DataManager.deleteBaseLububu(id);
      setLububus(DataManager.getBaseLububus());
      toast({
        title: "Success",
        description: "Base Labubu deleted successfully!",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete base Labubu.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Base Lububus</h3>
        <Button
          onClick={() => setIsAdding(!isAdding)}
          size="sm"
          variant={isAdding ? "outline" : "default"}
        >
          <Plus className="w-4 h-4 mr-2" />
          {isAdding ? "Cancel" : "Add New"}
        </Button>
      </div>

      {isAdding && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Add New Base Labubu</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newLububu.name}
                onChange={(e) => setNewLububu({ ...newLububu, name: e.target.value })}
                placeholder="e.g., Classic Labubu"
              />
            </div>
            <div>
              <FileUpload
                label="Base Labubu Image"
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                preview={newLububu.imageData || undefined}
                maxSize={5}
              />
            </div>
            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={newLububu.description}
                onChange={(e) => setNewLububu({ ...newLububu, description: e.target.value })}
                placeholder="Describe this base Labubu style..."
                rows={3}
              />
            </div>
            <Button onClick={handleAdd} className="w-full">
              Add Base Labubu
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {lububus.map((labubu) => (
          <Card key={labubu.id} className="relative">
            <CardContent className="p-4">
              <div className="aspect-[2/3] relative mb-3 rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={labubu.imageData}
                  alt={labubu.name}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
              <h4 className="font-semibold text-sm mb-1">{labubu.name}</h4>
              {labubu.description && (
                <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                  {labubu.description}
                </p>
              )}
              <Button
                onClick={() => handleDelete(labubu.id)}
                size="sm"
                variant="destructive"
                className="w-full"
              >
                <Trash2 className="w-3 h-3 mr-1" />
                Delete
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {lububus.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <Upload className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No base Lububus yet. Add your first one!</p>
        </div>
      )}
    </div>
  );
}
