// src/ai/flows/generate-labubu-fortune.ts
'use server';

/**
 * @fileOverview Generates a fortune related to a Labubu image.
 *
 * - generateLabubuFortune - A function that generates a fortune based on the Labubu image.
 * - GenerateLabubuFortuneInput - The input type for the generateLabubuFortune function.
 * - GenerateLabubuFortuneOutput - The return type for the generateLabubuFortune function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateLabubuFortuneInputSchema = z.object({
  labubuImageUrl: z
    .string()
    .describe(
      "A URL of a Labubu image."
    ),
});
export type GenerateLabubuFortuneInput = z.infer<typeof GenerateLabubuFortuneInputSchema>;

const GenerateLabubuFortuneOutputSchema = z.object({
  fortune: z.string().describe("A fortune related to the Labubu's appearance."),
});
export type GenerateLabubuFortuneOutput = z.infer<typeof GenerateLabubuFortuneOutputSchema>;

export async function generateLabubuFortune(input: GenerateLabubuFortuneInput): Promise<GenerateLabubuFortuneOutput> {
  return generateLabubuFortuneFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateLabubuFortunePrompt',
  input: {schema: GenerateLabubuFortuneInputSchema},
  output: {schema: GenerateLabubuFortuneOutputSchema},
  prompt: `You are a fortune teller who specializes in Labubu fortunes.  Labubu are small, mischievous creatures known for their unique fashion sense and playful nature.  Their fortunes are usually lighthearted and fun.

  Based on the appearance of the Labubu in the provided image, generate a fortune that is somewhat related to its appearance, features, or overall vibe. Be brief and whimsical.

  Image: {{media url=labubuImageUrl}}
  `,
});

const generateLabubuFortuneFlow = ai.defineFlow(
  {
    name: 'generateLabubuFortuneFlow',
    inputSchema: GenerateLabubuFortuneInputSchema,
    outputSchema: GenerateLabubuFortuneOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
