// src/ai/flows/generate-labubu-image.ts
'use server';

import { GoogleGenAI } from '@google/genai';
import mime from 'mime';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { BaseLububu, Persona, GenerationResult } from '@/types/labubu';

interface GenerateLabubuImageInput {
  baseLububu: BaseLububu;
  persona: Persona;
}

export async function generateLabubuImage(input: GenerateLabubuImageInput): Promise<GenerationResult> {
  const ai = new GoogleGenAI({
    apiKey: process.env.GEMINI_API_KEY!,
  });

  const config = {
    responseModalities: ['IMAGE', 'TEXT'] as const,
  };

  const model = 'gemini-2.5-flash-image-preview';

  // Create a comprehensive prompt combining the persona's AI prompt with the base labubu
  const combinedPrompt = `
Create a unique Labubu character by combining these elements:

BASE LABUBU STYLE: ${input.baseLububu.description || input.baseLububu.name}
PERSONA: ${input.persona.name} - ${input.persona.personality}

SPECIFIC INSTRUCTIONS: ${input.persona.aiPrompt}

Additional requirements:
- Keep the core Labubu aesthetic (small, toy-like, collectible figure)
- Blend the base Labubu's style with the persona's characteristics
- Make it look like a real collectible toy that could exist
- High quality, detailed, professional toy photography style
- Clean background, good lighting
- The character should embody both the base Labubu's form and the persona's spirit

Also provide a short, whimsical description of this unique Labubu character and what makes it special (2-3 sentences).
`;

  // Prepare content parts with images
  const contentParts: any[] = [
    {
      text: combinedPrompt,
    },
  ];

  // Add base labubu image if available
  if (input.baseLububu.imageData) {
    // Extract base64 data from data URL
    const base64Data = input.baseLububu.imageData.split(',')[1];
    contentParts.push({
      inlineData: {
        mimeType: input.baseLububu.imageType,
        data: base64Data,
      },
    });
  }

  // Add persona face image if available
  if (input.persona.faceImageData) {
    const base64Data = input.persona.faceImageData.split(',')[1];
    contentParts.push({
      inlineData: {
        mimeType: input.persona.faceImageType || 'image/jpeg',
        data: base64Data,
      },
    });
  }

  const contents = [
    {
      role: 'user' as const,
      parts: contentParts,
    },
  ];

  try {
    const response = await ai.models.generateContentStream({
      model,
      config,
      contents,
    });

    let generatedText = '';
    let imageBuffer: Buffer | null = null;
    let fileExtension = 'png';

    for await (const chunk of response) {
      if (!chunk.candidates || !chunk.candidates[0].content || !chunk.candidates[0].content.parts) {
        continue;
      }

      // Handle image data
      if (chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData) {
        const inlineData = chunk.candidates[0].content.parts[0].inlineData;
        fileExtension = mime.getExtension(inlineData.mimeType || 'image/png') || 'png';
        imageBuffer = Buffer.from(inlineData.data || '', 'base64');
      }
      // Handle text data
      else if (chunk.text) {
        generatedText += chunk.text;
      }
    }

    if (!imageBuffer) {
      throw new Error('No image was generated');
    }

    // Save the image to public directory
    const fileName = `labubu-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExtension}`;
    const publicPath = join(process.cwd(), 'public', 'generated', fileName);
    
    // Ensure the directory exists
    const { mkdir } = await import('fs/promises');
    const dir = join(process.cwd(), 'public', 'generated');
    try {
      await mkdir(dir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    await writeFile(publicPath, imageBuffer);

    const imageUrl = `/generated/${fileName}`;

    return {
      imageUrl,
      text: generatedText.trim() || `A unique ${input.persona.name} Labubu combining ${input.baseLububu.name} style with ${input.persona.personality.split('.')[0].toLowerCase()}.`,
    };

  } catch (error) {
    console.error('Error generating Labubu image:', error); // Updated error handling

    // Check if it's a quota/rate limit error and provide a fallback
    const errorMessage = error instanceof Error ? error.message : String(error);
    const isQuotaError = errorMessage.includes('quota') ||
                        errorMessage.includes('429') ||
                        errorMessage.includes('RESOURCE_EXHAUSTED') ||
                        errorMessage.includes('Too Many Requests');

    if (isQuotaError) {
      console.log('Quota limit reached, using fallback...');
      // Return a fallback response with the base image and a generated description
      return {
        imageUrl: input.baseLububu.imageData, // Use the base labubu image as fallback
        text: `Meet ${input.persona.name}'s ${input.baseLububu.name}! This unique creation embodies ${input.persona.personality.split('.')[0].toLowerCase()}. A truly special collectible that combines the classic Labubu charm with ${input.persona.name}'s distinctive spirit. (Note: AI image generation temporarily unavailable due to quota limits - showing base design)`,
      };
    }

    throw new Error(`Failed to generate Labubu image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
