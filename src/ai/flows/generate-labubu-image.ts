// src/ai/flows/generate-labubu-image.ts
'use server';

import OpenAI from 'openai';
import mime from 'mime';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { BaseLububu, Persona, GenerationResult } from '@/types/labubu';

interface GenerateLabubuImageInput {
  baseLububu: BaseLububu;
  persona: Persona;
}

export async function generateLabubuImage(input: GenerateLabubuImageInput): Promise<GenerationResult> {
  const openai = new OpenAI({
    baseURL: "https://openrouter.ai/api/v1",
    apiKey: process.env.OPENROUTER_API_KEY!,
  });

  const model = 'google/gemini-2.5-flash-image-preview';

  // Create a comprehensive prompt combining the persona's AI prompt with the base labubu
  const combinedPrompt = `
Create a unique Labubu character by combining these elements:

BASE LABUBU STYLE: ${input.baseLububu.description || input.baseLububu.name}
PERSONA: ${input.persona.name} - ${input.persona.personality}

SPECIFIC INSTRUCTIONS: ${input.persona.aiPrompt}

Additional requirements:
- Keep the core Labubu aesthetic (small, toy-like, collectible figure)
- Blend the base Labubu's style with the persona's characteristics
- Make it look like a real collectible toy that could exist
- High quality, detailed, professional toy photography style
- Clean background, good lighting
- The character should embody both the base Labubu's form and the persona's spirit

Also provide a short, whimsical description of this unique Labubu character and what makes it special (2-3 sentences).
`;

  // Prepare content parts with images for OpenAI format
  const contentParts: any[] = [
    {
      type: "text",
      text: combinedPrompt,
    },
  ];

  // Add base labubu image if available
  if (input.baseLububu.imageData) {
    contentParts.push({
      type: "image_url",
      image_url: {
        url: input.baseLububu.imageData, // Use the full data URL
      },
    });
  }

  // Add persona face image if available
  if (input.persona.faceImageData) {
    contentParts.push({
      type: "image_url",
      image_url: {
        url: input.persona.faceImageData, // Use the full data URL
      },
    });
  }

  const messages = [
    {
      role: 'user' as const,
      content: contentParts,
    },
  ];

  try {
    const completion = await openai.chat.completions.create({
      model,
      messages,
    });

    let generatedText = '';
    let imageBuffer: Buffer | null = null;
    let fileExtension = 'png';

    // Extract the response content
    const responseContent = completion.choices[0]?.message?.content;

    if (typeof responseContent === 'string') {
      generatedText = responseContent;
    }

    // Note: OpenAI's chat completions API doesn't directly generate images
    // For image generation, we would need to use a different approach
    // Since we're using gemini-2.5-flash-image-preview through OpenRouter,
    // we need to check if the response contains image data in a different format

    // For now, we'll handle this as text-only response and create a fallback
    // The actual image generation capability depends on the model's response format

    // For now, since we're not getting image data directly from the chat completion,
    // we'll return a fallback response with the base image and generated text
    if (!imageBuffer) {
      console.log('No image was generated from the model, using fallback...');
      return {
        imageUrl: input.baseLububu.imageData, // Use the base labubu image as fallback
        text: generatedText.trim() || `Meet ${input.persona.name}'s ${input.baseLububu.name}! This unique creation embodies ${input.persona.personality.split('.')[0].toLowerCase()}. A truly special collectible that combines the classic Labubu charm with ${input.persona.name}'s distinctive spirit.`,
      };
    }

    // Save the image to public directory (if we had image data)
    const fileName = `labubu-${Date.now()}-${Math.random().toString(36).substring(2, 11)}.${fileExtension}`;
    const publicPath = join(process.cwd(), 'public', 'generated', fileName);

    // Ensure the directory exists
    const { mkdir } = await import('fs/promises');
    const dir = join(process.cwd(), 'public', 'generated');
    try {
      await mkdir(dir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    await writeFile(publicPath, imageBuffer);

    const imageUrl = `/generated/${fileName}`;

    return {
      imageUrl,
      text: generatedText.trim() || `A unique ${input.persona.name} Labubu combining ${input.baseLububu.name} style with ${input.persona.personality.split('.')[0].toLowerCase()}.`,
    };

  } catch (error) {
    console.error('Error generating Labubu image:', error); // Updated error handling

    // Check if it's a quota/rate limit error and provide a fallback
    const errorMessage = error instanceof Error ? error.message : String(error);
    const isQuotaError = errorMessage.includes('quota') ||
                        errorMessage.includes('429') ||
                        errorMessage.includes('RESOURCE_EXHAUSTED') ||
                        errorMessage.includes('Too Many Requests');

    if (isQuotaError) {
      console.log('Quota limit reached, using fallback...');
      // Return a fallback response with the base image and a generated description
      return {
        imageUrl: input.baseLububu.imageData, // Use the base labubu image as fallback
        text: `Meet ${input.persona.name}'s ${input.baseLububu.name}! This unique creation embodies ${input.persona.personality.split('.')[0].toLowerCase()}. A truly special collectible that combines the classic Labubu charm with ${input.persona.name}'s distinctive spirit. (Note: AI image generation temporarily unavailable due to quota limits - showing base design)`,
      };
    }

    throw new Error(`Failed to generate Labubu image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
