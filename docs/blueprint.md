# **App Name**: Lucky Labubu

## Core Features:

- Image URL Input: Allow users to input a list of image URLs for the Labubu images.
- Random Image Selection: Select a random Labubu image from the provided URLs.
- Package Opening Animation: Display an animation of a package opening when the 'Generate' button is clicked.
- Image Rotation Animation: Implement a rotating animation for the image as the package opens. 
- Fortune Generation: After image reveal, use a tool to generate a random fortune based on the Labubu image.  This involves reasoning on whether the provided labubu suggests a specific class of fortune.
- Chat-like Fortune Display: Display the fortune in a chat-like interface with a typing animation.
- Image Slide-Out: Once the image is revealed, smoothly slide it to the left to make room for the fortune.

## Style Guidelines:

- Primary color: Playful Lavender (#E6E6FA) for a whimsical and engaging feel.
- Background color: Light off-white (#F5F5F5) provides a clean and unobtrusive backdrop, ensuring focus remains on the Labubu images and fortunes. 
- Accent color: Soft pink (#FFB6C1) to highlight interactive elements and create a visually appealing contrast.
- Body and headline font: 'PT Sans' for clear readability and a modern, friendly appearance.
- Use simple, animated icons to enhance user interaction and guide the user experience.
- Clean and intuitive layout with a focus on visual hierarchy. The 'Generate' button should be prominent.
- Smooth and engaging animations for package opening, image rotation, sliding, and fortune typing.